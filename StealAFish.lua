-- Steal A Fish WindUI Script
-- Load WindUI Library
local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Variables
local LocalPlayer = Players.LocalPlayer
local Character = LocalPlayer.Character or LocalPlayer.CharacterAdded:Wait()
local Humanoid = Character:WaitForChild("Humanoid")
local RootPart = Character:WaitForChild("HumanoidRootPart")

-- Player Variables
local originalWalkSpeed = Humanoid.WalkSpeed
local originalJumpPower = Humanoid.JumpPower
local noclipEnabled = false
local infJumpEnabled = false
local noclipConnection

-- Auto Lock Variables
local autoLockEnabled = false
local autoLockConnection
local playerTycoon = nil

-- Auto Collect Variables
local autoCollectEnabled = false
local autoCollectConnection
local autoCollectDelay = 0.5
local isCollecting = false
local lastCollectTime = 0

-- Instant Proximity Variables
local instantProximityEnabled = false
local proximityPrompts = {}
local currentHoldDuration = 0.1
local proximityConnection
local isScanning = false

-- Countdown ESP Variables
local countdownESPEnabled = false
local countdownESPGuis = {}
local countdownUpdateConnection

-- Player ESP Variables
local playerESPEnabled = false
local playerESPGuis = {}
local playerESPConnection

-- Create Window
local Window = WindUI:CreateWindow({
    Title = "Steal A Fish Hub",
    Icon = "fish",
    Author = "WindUI Script",
    Folder = "StealAFishHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    Resizable = true,
    SideBarWidth = 200,
})

-- Create Tabs
local MainTab = Window:Tab({
    Title = "Main",
    Icon = "home",
})

local PlayerTab = Window:Tab({
    Title = "Player",
    Icon = "user",
})

local TeleportTab = Window:Tab({
    Title = "Teleport",
    Icon = "map-pin",
})

local ESPTab = Window:Tab({
    Title = "ESP",
    Icon = "eye",
})

local SettingsTab = Window:Tab({
    Title = "Settings",
    Icon = "settings",
})

-- MAIN TAB (Placeholders)
MainTab:Toggle({
    Title = "Auto Lock",
    Desc = "Automatically lock onto targets",
    Icon = "target",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        autoLockEnabled = state
        
        if autoLockEnabled then
            autoLockConnection = RunService.Heartbeat:Connect(function()
                if not workspace:FindFirstChild("Map") or not workspace.Map:FindFirstChild("Tycoons") then
                    return
                end

                for i = 1, 8 do
                    local tycoon = workspace.Map.Tycoons:FindFirstChild("Tycoon" .. i)
                    if tycoon then
                        local boardPart = nil
                        local usernameLabel = nil

                        -- Method 1: Look for Board folder/group
                        if tycoon:FindFirstChild("Board") then
                            for _, child in pairs(tycoon.Board:GetChildren()) do
                                if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                    boardPart = child
                                    usernameLabel = child.SurfaceGui.Username
                                    break
                                end
                            end
                        end

                        -- Method 2: Look for direct board part in tycoon
                        if not boardPart then
                            for _, child in pairs(tycoon:GetChildren()) do
                                if child:IsA("BasePart") and child.Name:lower():find("board") and
                                   child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                    boardPart = child
                                    usernameLabel = child.SurfaceGui.Username
                                    break
                                end
                            end
                        end

                        -- Method 3: Look for any part with Username SurfaceGui
                        if not boardPart then
                            local function searchForBoard(parent)
                                for _, child in pairs(parent:GetChildren()) do
                                    if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and
                                       child.SurfaceGui:FindFirstChild("Username") then
                                        return child, child.SurfaceGui.Username
                                    elseif child:IsA("Model") or child:IsA("Folder") then
                                        local found, label = searchForBoard(child)
                                        if found then return found, label end
                                    end
                                end
                                return nil, nil
                            end
                            boardPart, usernameLabel = searchForBoard(tycoon)
                        end

                        if boardPart and usernameLabel and usernameLabel.Text == LocalPlayer.Name then
                            playerTycoon = tycoon

                            -- Check multiple possible paths for the forcefield system
                            local timeLabel = nil
                            local forceFieldBuy = nil

                            -- Method 1: Check direct path in tycoon (original structure)
                            if tycoon:FindFirstChild("ForcefieldFolder") and
                               tycoon.ForcefieldFolder:FindFirstChild("Screen") and
                               tycoon.ForcefieldFolder.Screen:FindFirstChild("Screen") and
                               tycoon.ForcefieldFolder.Screen.Screen:FindFirstChild("SurfaceGui") and
                               tycoon.ForcefieldFolder.Screen.Screen.SurfaceGui:FindFirstChild("Time") then

                                timeLabel = tycoon.ForcefieldFolder.Screen.Screen.SurfaceGui.Time

                                if tycoon.ForcefieldFolder:FindFirstChild("Buttons") and
                                   tycoon.ForcefieldFolder.Buttons:FindFirstChild("ForceFieldBuy") and
                                   tycoon.ForcefieldFolder.Buttons.ForceFieldBuy:FindFirstChild("Forcefield") then
                                    forceFieldBuy = tycoon.ForcefieldFolder.Buttons.ForceFieldBuy.Forcefield
                                end
                            end

                            -- Method 2: Check nested Tycoon structure (your mentioned path)
                            if not timeLabel and tycoon:FindFirstChild("Tycoon") then
                                local innerTycoon = tycoon.Tycoon
                                if innerTycoon:FindFirstChild("ForcefieldFolder") and
                                   innerTycoon.ForcefieldFolder:FindFirstChild("Screen") and
                                   innerTycoon.ForcefieldFolder.Screen:FindFirstChild("Screen") and
                                   innerTycoon.ForcefieldFolder.Screen.Screen:FindFirstChild("SurfaceGui") and
                                   innerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui:FindFirstChild("Time") then

                                    timeLabel = innerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui.Time

                                    if innerTycoon.ForcefieldFolder:FindFirstChild("Buttons") and
                                       innerTycoon.ForcefieldFolder.Buttons:FindFirstChild("ForceFieldBuy") and
                                       innerTycoon.ForcefieldFolder.Buttons.ForceFieldBuy:FindFirstChild("Forcefield") then
                                        forceFieldBuy = innerTycoon.ForcefieldFolder.Buttons.ForceFieldBuy.Forcefield
                                    end
                                end
                            end

                            -- Auto teleport when countdown reaches 0s
                            if timeLabel and forceFieldBuy then
                                if timeLabel.Text == "0s" then
                                    if RootPart then
                                        print("Auto Lock: Teleporting to Forcefield!")
                                        RootPart.CFrame = forceFieldBuy.CFrame
                                        wait(0.1) -- Small delay to ensure teleport completes
                                    end
                                end
                            end
                            break
                        end
                    end
                end
            end)
        else
            if autoLockConnection then
                autoLockConnection:Disconnect()
                autoLockConnection = nil
            end
            playerTycoon = nil
        end
    end
})

MainTab:Toggle({
    Title = "Instant Proximity",
    Desc = "Instant proximity detection",
    Icon = "zap",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        instantProximityEnabled = state
        
        if instantProximityEnabled then
            local function scanForProximityPrompts()
                if isScanning then return end
                isScanning = true
                
                local function scanDescendants(parent, startTime)
                    for _, child in pairs(parent:GetChildren()) do
                        if child:IsA("ProximityPrompt") then
                            if not proximityPrompts[child] then
                                proximityPrompts[child] = true
                                child.HoldDuration = currentHoldDuration
                                
                                child.AncestryChanged:Connect(function()
                                    if not child.Parent then
                                        proximityPrompts[child] = nil
                                    end
                                end)
                            end
                        end
                        
                        if tick() - startTime > 0.001 then
                            RunService.Heartbeat:Wait()
                            startTime = tick()
                        end
                        
                        if #child:GetChildren() > 0 then
                            scanDescendants(child, startTime)
                        end
                    end
                end
                
                scanDescendants(workspace, tick())
                isScanning = false
            end
            
            local function updateAllPrompts()
                for prompt, _ in pairs(proximityPrompts) do
                    if prompt and prompt.Parent then
                        prompt.HoldDuration = currentHoldDuration
                    end
                end
            end
            
            scanForProximityPrompts()
            
            MainTab:Slider({
                Title = "Hold Duration",
                Desc = "Adjust proximity prompt hold duration",
                Step = 0.01,
                Value = {
                    Min = 0,
                    Max = 1,
                    Default = 0.1,
                },
                Callback = function(value)
                    currentHoldDuration = value
                    updateAllPrompts()
                end
            })
            
            workspace.DescendantAdded:Connect(function(descendant)
                if instantProximityEnabled and descendant:IsA("ProximityPrompt") then
                    if not proximityPrompts[descendant] then
                        proximityPrompts[descendant] = true
                        descendant.HoldDuration = currentHoldDuration
                        
                        descendant.AncestryChanged:Connect(function()
                            if not descendant.Parent then
                                proximityPrompts[descendant] = nil
                            end
                        end)
                    end
                end
            end)
            
            spawn(function()
                while instantProximityEnabled do
                    wait(2)
                    if not isScanning then
                        scanForProximityPrompts()
                    end
                end
            end)
            
        else
            proximityPrompts = {}
        end
    end
})

MainTab:Toggle({
    Title = "Countdown ESP",
    Desc = "Display tycoon forcefield countdowns",
    Icon = "clock",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        countdownESPEnabled = state
        
        if countdownESPEnabled then
            local function createCountdownESP(tycoon, tycoonNumber)
                pcall(function()
                    if countdownESPGuis[tycoonNumber] then
                        if countdownESPGuis[tycoonNumber].gui and countdownESPGuis[tycoonNumber].gui.Parent then
                            countdownESPGuis[tycoonNumber].gui:Destroy()
                        end
                        countdownESPGuis[tycoonNumber] = nil
                    end
                    
                    local screenPart = nil
                    local timeLabel = nil
                    
                    if tycoon and tycoon.Parent then
                        if tycoon:FindFirstChild("ForcefieldFolder") then
                            local forcefieldFolder = tycoon.ForcefieldFolder
                            if forcefieldFolder:FindFirstChild("Screen") then
                                local screenFolder = forcefieldFolder.Screen
                                if screenFolder:FindFirstChild("Screen") then
                                    screenPart = screenFolder.Screen
                                    if screenPart:FindFirstChild("SurfaceGui") then
                                        local surfaceGui = screenPart.SurfaceGui
                                        if surfaceGui:FindFirstChild("Time") then
                                            timeLabel = surfaceGui.Time
                                        end
                                    end
                                end
                            end
                        end
                        
                        if not screenPart or not timeLabel then
                            if tycoon:FindFirstChild("Tycoon") then
                                local innerTycoon = tycoon.Tycoon
                                if innerTycoon:FindFirstChild("ForcefieldFolder") then
                                    local forcefieldFolder = innerTycoon.ForcefieldFolder
                                    if forcefieldFolder:FindFirstChild("Screen") then
                                        local screenFolder = forcefieldFolder.Screen
                                        if screenFolder:FindFirstChild("Screen") then
                                            screenPart = screenFolder.Screen
                                            if screenPart:FindFirstChild("SurfaceGui") then
                                                local surfaceGui = screenPart.SurfaceGui
                                                if surfaceGui:FindFirstChild("Time") then
                                                    timeLabel = surfaceGui.Time
                                                end
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                    
                    if screenPart and timeLabel and screenPart.Parent then
                        local existingESP = screenPart:FindFirstChild("CountdownESP")
                        if existingESP then
                            existingESP:Destroy()
                        end
                        
                        local espGui = Instance.new("SurfaceGui")
                        espGui.Name = "CountdownESP"
                        espGui.Face = Enum.NormalId.Front
                        espGui.Parent = screenPart
                        
                        local espLabel = Instance.new("TextLabel")
                        espLabel.Name = "CountdownLabel"
                        espLabel.Size = UDim2.new(1, 0, 1, 0)
                        espLabel.Position = UDim2.new(0, 0, 0, 0)
                        espLabel.BackgroundTransparency = 1
                        espLabel.Text = timeLabel.Text or "N/A"
                        espLabel.TextColor3 = Color3.new(1, 1, 0)
                        espLabel.TextScaled = true
                        espLabel.Font = Enum.Font.SourceSansBold
                        espLabel.TextStrokeTransparency = 0
                        espLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
                        espLabel.Parent = espGui
                        
                        countdownESPGuis[tycoonNumber] = {
                            gui = espGui,
                            label = espLabel,
                            timeLabel = timeLabel,
                            screenPart = screenPart
                        }
                    end
                end)
            end
            
            local function updateCountdownESP()
                pcall(function()
                    if not countdownESPEnabled then return end
                    
                    if not workspace:FindFirstChild("Map") or not workspace.Map:FindFirstChild("Tycoons") then
                        return
                    end
                    
                    for i = 1, 8 do
                        local tycoon = workspace.Map.Tycoons:FindFirstChild("Tycoon" .. i)
                        if tycoon and tycoon.Parent then
                            if not countdownESPGuis[i] or not countdownESPGuis[i].gui or not countdownESPGuis[i].gui.Parent then
                                createCountdownESP(tycoon, i)
                            end
                            
                            local espData = countdownESPGuis[i]
                            if espData and espData.timeLabel and espData.timeLabel.Parent and espData.label and espData.label.Parent then
                                local success, currentTime = pcall(function()
                                    return espData.timeLabel.Text or "N/A"
                                end)
                                
                                if success and currentTime then
                                    espData.label.Text = currentTime
                                    
                                    if currentTime == "0s" then
                                        espData.label.TextColor3 = Color3.new(1, 0, 0)
                                    else
                                        local timeNumber = tonumber(currentTime:gsub("s", ""))
                                        if timeNumber and timeNumber <= 3 then
                                            espData.label.TextColor3 = Color3.new(1, 0.5, 0)
                                        else
                                            espData.label.TextColor3 = Color3.new(1, 1, 0)
                                        end
                                    end
                                end
                            else
                                countdownESPGuis[i] = nil
                            end
                        end
                    end
                end)
            end
            
            countdownUpdateConnection = RunService.Heartbeat:Connect(updateCountdownESP)
            spawn(function()
                wait(0.5)
                updateCountdownESP()
            end)
            
        else
            if countdownUpdateConnection then
                countdownUpdateConnection:Disconnect()
                countdownUpdateConnection = nil
            end
            
            for i, espData in pairs(countdownESPGuis) do
                pcall(function()
                    if espData and espData.gui and espData.gui.Parent then
                        espData.gui:Destroy()
                    end
                end)
            end
            countdownESPGuis = {}
        end
    end
})

MainTab:Toggle({
    Title = "Auto Collect",
    Desc = "Automatically collect from tycoon buttons",
    Icon = "zap-off",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        autoCollectEnabled = state
        
        if autoCollectEnabled then
            local function findPlayerTycoon()
                if not workspace:FindFirstChild("Map") or not workspace.Map:FindFirstChild("Tycoons") then
                    return nil
                end

                for i = 1, 8 do
                    local tycoon = workspace.Map.Tycoons:FindFirstChild("Tycoon" .. i)
                    if tycoon then
                        local boardPart = nil
                        local usernameLabel = nil

                        if tycoon:FindFirstChild("Board") then
                            for _, child in pairs(tycoon.Board:GetChildren()) do
                                if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                    boardPart = child
                                    usernameLabel = child.SurfaceGui.Username
                                    break
                                end
                            end
                        end

                        if not boardPart then
                            for _, child in pairs(tycoon:GetChildren()) do
                                if child:IsA("BasePart") and child.Name:lower():find("board") and
                                   child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                    boardPart = child
                                    usernameLabel = child.SurfaceGui.Username
                                    break
                                end
                            end
                        end

                        if not boardPart then
                            local function searchForBoard(parent)
                                for _, child in pairs(parent:GetChildren()) do
                                    if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and
                                       child.SurfaceGui:FindFirstChild("Username") then
                                        return child, child.SurfaceGui.Username
                                    elseif child:IsA("Model") or child:IsA("Folder") then
                                        local found, label = searchForBoard(child)
                                        if found then return found, label end
                                    end
                                end
                                return nil, nil
                            end
                            boardPart, usernameLabel = searchForBoard(tycoon)
                        end

                        if boardPart and usernameLabel and usernameLabel.Text == LocalPlayer.Name then
                            return tycoon
                        end
                    end
                end
                return nil
            end

            local function collectFromButtons()
                if isCollecting or not autoCollectEnabled then return end
                
                local currentTime = tick()
                if currentTime - lastCollectTime < 2 then return end
                
                isCollecting = true
                lastCollectTime = currentTime
                
                spawn(function()
                    local currentTycoon = findPlayerTycoon()
                    
                    if currentTycoon then
                        local innerTycoon = currentTycoon:FindFirstChild("Tycoon")
                        local slotsFolder = nil
                        
                        if innerTycoon then
                            slotsFolder = innerTycoon:FindFirstChild("Slots")
                        else
                            slotsFolder = currentTycoon:FindFirstChild("Slots")
                        end
                        
                        if slotsFolder then
                            local leftSlots = slotsFolder:FindFirstChild("Left")
                            local rightSlots = slotsFolder:FindFirstChild("Right")
                            
                            if leftSlots and leftSlots:FindFirstChild("Slots") then
                                for _, slot in pairs(leftSlots.Slots:GetChildren()) do
                                    if slot.Name == "Slot" and autoCollectEnabled then
                                        local button = slot:FindFirstChild("Button")
                                        if button and button:IsA("BasePart") and RootPart then
                                            RootPart.CFrame = button.CFrame
                                            wait(autoCollectDelay)
                                        end
                                    end
                                end
                            end
                            
                            if rightSlots and rightSlots:FindFirstChild("Slots") then
                                for _, slot in pairs(rightSlots.Slots:GetChildren()) do
                                    if slot.Name == "Slot" and autoCollectEnabled then
                                        local button = slot:FindFirstChild("Button")
                                        if button and button:IsA("BasePart") and RootPart then
                                            RootPart.CFrame = button.CFrame
                                            wait(autoCollectDelay)
                                        end
                                    end
                                end
                            end
                        end
                    end
                    
                    isCollecting = false
                end)
            end

            autoCollectConnection = RunService.Heartbeat:Connect(function()
                if autoCollectEnabled and not isCollecting then
                    collectFromButtons()
                end
            end)
        else
            if autoCollectConnection then
                autoCollectConnection:Disconnect()
                autoCollectConnection = nil
            end
            isCollecting = false
        end
    end
})

MainTab:Slider({
    Title = "Auto Collect Delay",
    Desc = "Delay between button teleports",
    Step = 0.1,
    Value = {
        Min = 0.1,
        Max = 2.0,
        Default = 0.5,
    },
    Callback = function(value)
        autoCollectDelay = value
    end
})

MainTab:Toggle({
    Title = "Full Bright",
    Desc = "Remove darkness from the game",
    Icon = "sun",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Full Bright:", state)
        -- TODO: Implement Full Bright functionality
    end
})

MainTab:Button({
    Title = "Auto Collect Debug",
    Desc = "Show auto collect system debug info",
    Icon = "bug",
    Callback = function()
        local debugInfo = "=== AUTO COLLECT DEBUG INFO ===\n"
        debugInfo = debugInfo .. "Player Name: " .. LocalPlayer.Name .. "\n"
        debugInfo = debugInfo .. "Auto Collect Status: " .. (autoCollectEnabled and "Enabled" or "Disabled") .. "\n"
        debugInfo = debugInfo .. "Auto Collect Delay: " .. autoCollectDelay .. "s\n"
        debugInfo = debugInfo .. "Looking for player tycoon...\n"
        
        if not workspace:FindFirstChild("Map") then
            debugInfo = debugInfo .. "ERROR: workspace.Map not found\n"
        elseif not workspace.Map:FindFirstChild("Tycoons") then
            debugInfo = debugInfo .. "ERROR: workspace.Map.Tycoons not found\n"
        else
            debugInfo = debugInfo .. "Found workspace.Map.Tycoons\n"
            
            local currentTycoon = nil
            
            for i = 1, 8 do
                local tycoon = workspace.Map.Tycoons:FindFirstChild("Tycoon" .. i)
                if tycoon then
                    debugInfo = debugInfo .. "Found " .. tycoon.Name .. "\n"
                    
                    local boardPart = nil
                    local usernameLabel = nil

                    if tycoon:FindFirstChild("Board") then
                        debugInfo = debugInfo .. "  - Has Board folder\n"
                        for _, child in pairs(tycoon.Board:GetChildren()) do
                            if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                boardPart = child
                                usernameLabel = child.SurfaceGui.Username
                                debugInfo = debugInfo .. "    - Found board part with Username: " .. usernameLabel.Text .. "\n"
                                break
                            end
                        end
                    else
                        debugInfo = debugInfo .. "  - No Board folder found\n"
                    end

                    if not boardPart then
                        debugInfo = debugInfo .. "  - Searching for board part directly in tycoon...\n"
                        for _, child in pairs(tycoon:GetChildren()) do
                            if child:IsA("BasePart") and child.Name:lower():find("board") then
                                debugInfo = debugInfo .. "    - Found part with 'board' in name: " .. child.Name .. "\n"
                                if child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                    boardPart = child
                                    usernameLabel = child.SurfaceGui.Username
                                    debugInfo = debugInfo .. "    - Username: " .. usernameLabel.Text .. "\n"
                                    break
                                end
                            end
                        end
                    end

                    if not boardPart then
                        debugInfo = debugInfo .. "  - Searching recursively for Username SurfaceGui...\n"
                        local function searchForBoard(parent)
                            for _, child in pairs(parent:GetChildren()) do
                                if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and
                                   child.SurfaceGui:FindFirstChild("Username") then
                                    return child, child.SurfaceGui.Username
                                elseif child:IsA("Model") or child:IsA("Folder") then
                                    local found, label = searchForBoard(child)
                                    if found then return found, label end
                                end
                            end
                            return nil, nil
                        end
                        boardPart, usernameLabel = searchForBoard(tycoon)
                        if boardPart and usernameLabel then
                            debugInfo = debugInfo .. "    - Found Username recursively: " .. usernameLabel.Text .. "\n"
                        end
                    end

                    if boardPart and usernameLabel and usernameLabel.Text == LocalPlayer.Name then
                        debugInfo = debugInfo .. "  - MATCH! This is our tycoon!\n"
                        currentTycoon = tycoon
                        break
                    end
                else
                    debugInfo = debugInfo .. "Tycoon" .. i .. " not found\n"
                end
            end
            
            if currentTycoon then
                debugInfo = debugInfo .. "Player Tycoon: " .. currentTycoon.Name .. "\n"
                
                local slotsFolder = currentTycoon:FindFirstChild("Slots")
                if slotsFolder then
                    debugInfo = debugInfo .. "  - Found Slots folder\n"
                    
                    local leftSlots = slotsFolder:FindFirstChild("Left")
                    local rightSlots = slotsFolder:FindFirstChild("Right")
                    
                    if leftSlots then
                        debugInfo = debugInfo .. "  - Found Left slots\n"
                        if leftSlots:FindFirstChild("Slots") then
                            debugInfo = debugInfo .. "    - Found Left.Slots folder\n"
                            local leftSlotCount = 0
                            local leftButtonCount = 0
                            for _, slot in pairs(leftSlots.Slots:GetChildren()) do
                                if slot.Name == "Slot" then
                                    leftSlotCount = leftSlotCount + 1
                                    local button = slot:FindFirstChild("Button")
                                    if button and button:IsA("BasePart") then
                                        leftButtonCount = leftButtonCount + 1
                                        debugInfo = debugInfo .. "      - Slot " .. leftSlotCount .. ": Button found at " .. tostring(button.Position) .. "\n"
                                    else
                                        debugInfo = debugInfo .. "      - Slot " .. leftSlotCount .. ": No valid button\n"
                                    end
                                end
                            end
                            debugInfo = debugInfo .. "    - Total Left Slots: " .. leftSlotCount .. ", Valid Buttons: " .. leftButtonCount .. "\n"
                        else
                            debugInfo = debugInfo .. "    - No Left.Slots folder found\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No Left slots found\n"
                    end
                    
                    if rightSlots then
                        debugInfo = debugInfo .. "  - Found Right slots\n"
                        if rightSlots:FindFirstChild("Slots") then
                            debugInfo = debugInfo .. "    - Found Right.Slots folder\n"
                            local rightSlotCount = 0
                            local rightButtonCount = 0
                            for _, slot in pairs(rightSlots.Slots:GetChildren()) do
                                if slot.Name == "Slot" then
                                    rightSlotCount = rightSlotCount + 1
                                    local button = slot:FindFirstChild("Button")
                                    if button and button:IsA("BasePart") then
                                        rightButtonCount = rightButtonCount + 1
                                        debugInfo = debugInfo .. "      - Slot " .. rightSlotCount .. ": Button found at " .. tostring(button.Position) .. "\n"
                                    else
                                        debugInfo = debugInfo .. "      - Slot " .. rightSlotCount .. ": No valid button\n"
                                    end
                                end
                            end
                            debugInfo = debugInfo .. "    - Total Right Slots: " .. rightSlotCount .. ", Valid Buttons: " .. rightButtonCount .. "\n"
                        else
                            debugInfo = debugInfo .. "    - No Right.Slots folder found\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No Right slots found\n"
                    end
                else
                    debugInfo = debugInfo .. "  - No Slots folder found\n"
                    debugInfo = debugInfo .. "  - Tycoon children:\n"
                    for _, child in pairs(currentTycoon:GetChildren()) do
                        debugInfo = debugInfo .. "    - " .. child.Name .. " (" .. child.ClassName .. ")\n"
                    end
                end
            else
                debugInfo = debugInfo .. "Player Tycoon: Not found\n"
            end
        end
        
        if RootPart then
            debugInfo = debugInfo .. "Player Position: " .. tostring(RootPart.Position) .. "\n"
        else
            debugInfo = debugInfo .. "Player Position: RootPart not found\n"
        end
        
        debugInfo = debugInfo .. "=========================="
        
        print(debugInfo)
    end
})

MainTab:Button({
    Title = "Debug Info",
    Desc = "Show player tycoon and countdown info",
    Icon = "info",
    Callback = function()
        local debugInfo = "=== AUTO LOCK DEBUG INFO ===\n"
        debugInfo = debugInfo .. "Player Name: " .. LocalPlayer.Name .. "\n"
        debugInfo = debugInfo .. "Looking for tycoons...\n"
        
        -- Check if workspace.Map.Tycoons exists
        if not workspace:FindFirstChild("Map") then
            debugInfo = debugInfo .. "ERROR: workspace.Map not found\n"
        elseif not workspace.Map:FindFirstChild("Tycoons") then
            debugInfo = debugInfo .. "ERROR: workspace.Map.Tycoons not found\n"
        else
            debugInfo = debugInfo .. "Found workspace.Map.Tycoons\n"
            
            -- List all tycoons
            for i = 1, 8 do
                local tycoon = workspace.Map.Tycoons:FindFirstChild("Tycoon" .. i)
                if tycoon then
                    debugInfo = debugInfo .. "Found " .. tycoon.Name .. "\n"
                    
                    -- Enhanced board structure checking
                    local boardPart = nil
                    local usernameLabel = nil

                    -- Method 1: Check for Board folder/group
                    if tycoon:FindFirstChild("Board") then
                        debugInfo = debugInfo .. "  - Has Board (Group)\n"
                        for _, child in pairs(tycoon.Board:GetChildren()) do
                            debugInfo = debugInfo .. "    - Child: " .. child.Name .. " (" .. child.ClassName .. ")\n"
                            if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                boardPart = child
                                usernameLabel = child.SurfaceGui.Username
                                debugInfo = debugInfo .. "    - Found board part with Username!\n"
                                break
                            end
                        end
                    else
                        debugInfo = debugInfo .. "  - No Board folder found\n"
                    end

                    -- Method 2: Check for direct board part in tycoon
                    if not boardPart then
                        debugInfo = debugInfo .. "  - Searching for board part directly in tycoon...\n"
                        for _, child in pairs(tycoon:GetChildren()) do
                            if child:IsA("BasePart") and child.Name:lower():find("board") then
                                debugInfo = debugInfo .. "    - Found part with 'board' in name: " .. child.Name .. "\n"
                                if child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                    boardPart = child
                                    usernameLabel = child.SurfaceGui.Username
                                    debugInfo = debugInfo .. "    - This part has Username SurfaceGui!\n"
                                    break
                                end
                            end
                        end
                    end

                    -- Method 3: Search recursively for any part with Username
                    if not boardPart then
                        debugInfo = debugInfo .. "  - Searching recursively for Username SurfaceGui...\n"
                        local function searchForBoard(parent, depth)
                            if depth > 3 then return nil, nil end -- Limit recursion depth
                            for _, child in pairs(parent:GetChildren()) do
                                if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and
                                   child.SurfaceGui:FindFirstChild("Username") then
                                    debugInfo = debugInfo .. "    - Found Username at: " .. child:GetFullName() .. "\n"
                                    return child, child.SurfaceGui.Username
                                elseif child:IsA("Model") or child:IsA("Folder") then
                                    local found, label = searchForBoard(child, depth + 1)
                                    if found then return found, label end
                                end
                            end
                            return nil, nil
                        end
                        boardPart, usernameLabel = searchForBoard(tycoon, 0)
                    end

                    if boardPart and usernameLabel then
                        debugInfo = debugInfo .. "  - Found board part: " .. boardPart.Name .. " at " .. boardPart:GetFullName() .. "\n"
                        local username = usernameLabel.Text
                        debugInfo = debugInfo .. "  - Username: " .. username .. "\n"
                        if username == LocalPlayer.Name then
                            debugInfo = debugInfo .. "  - MATCH! This is our tycoon!\n"
                            playerTycoon = tycoon
                        end
                    else
                        debugInfo = debugInfo .. "  - No board part with Username SurfaceGui found anywhere\n"
                        -- List all children for debugging
                        debugInfo = debugInfo .. "  - All tycoon children:\n"
                        for _, child in pairs(tycoon:GetChildren()) do
                            debugInfo = debugInfo .. "    - " .. child.Name .. " (" .. child.ClassName .. ")\n"
                        end
                    end
                else
                    debugInfo = debugInfo .. "Tycoon" .. i .. " not found\n"
                end
            end
        end
        
        if playerTycoon then
            debugInfo = debugInfo .. "Player Tycoon: " .. playerTycoon.Name .. "\n"

            -- Check forcefield structure - Method 1 (Direct)
            local timeLabel = nil
            local forceFieldBuy = nil

            if playerTycoon:FindFirstChild("ForcefieldFolder") then
                debugInfo = debugInfo .. "  - Has ForcefieldFolder (Direct)\n"
                if playerTycoon.ForcefieldFolder:FindFirstChild("Screen") then
                    debugInfo = debugInfo .. "  - Has Screen\n"
                    if playerTycoon.ForcefieldFolder.Screen:FindFirstChild("Screen") then
                        debugInfo = debugInfo .. "  - Has Screen.Screen\n"
                        if playerTycoon.ForcefieldFolder.Screen.Screen:FindFirstChild("SurfaceGui") then
                            debugInfo = debugInfo .. "  - Has SurfaceGui\n"
                            if playerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui:FindFirstChild("Time") then
                                timeLabel = playerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui.Time
                                debugInfo = debugInfo .. "  - Found Time label (Direct): " .. timeLabel.Text .. "\n"
                            else
                                debugInfo = debugInfo .. "  - No Time label\n"
                            end
                        else
                            debugInfo = debugInfo .. "  - No SurfaceGui in Screen\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No Screen.Screen\n"
                    end
                else
                    debugInfo = debugInfo .. "  - No Screen\n"
                end

                -- Check for ForceFieldBuy button (Direct)
                if playerTycoon.ForcefieldFolder:FindFirstChild("Buttons") then
                    debugInfo = debugInfo .. "  - Has Buttons folder (Direct)\n"
                    if playerTycoon.ForcefieldFolder.Buttons:FindFirstChild("ForceFieldBuy") then
                        debugInfo = debugInfo .. "  - Found ForceFieldBuy button (Direct)\n"
                        if playerTycoon.ForcefieldFolder.Buttons.ForceFieldBuy:FindFirstChild("Forcefield") then
                            forceFieldBuy = playerTycoon.ForcefieldFolder.Buttons.ForceFieldBuy.Forcefield
                            debugInfo = debugInfo .. "  - Found Forcefield object (Direct)\n"
                        else
                            debugInfo = debugInfo .. "  - No Forcefield object in ForceFieldBuy\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No ForceFieldBuy button in Buttons\n"
                    end
                else
                    debugInfo = debugInfo .. "  - No Buttons folder\n"
                end
            else
                debugInfo = debugInfo .. "  - No ForcefieldFolder (Direct)\n"
            end

            -- Check forcefield structure - Method 2 (Nested Tycoon)
            if playerTycoon:FindFirstChild("Tycoon") then
                debugInfo = debugInfo .. "  - Has nested Tycoon folder\n"
                local innerTycoon = playerTycoon.Tycoon

                if innerTycoon:FindFirstChild("ForcefieldFolder") then
                    debugInfo = debugInfo .. "  - Has ForcefieldFolder (Nested)\n"
                    if innerTycoon.ForcefieldFolder:FindFirstChild("Screen") then
                        debugInfo = debugInfo .. "  - Has Screen (Nested)\n"
                        if innerTycoon.ForcefieldFolder.Screen:FindFirstChild("Screen") then
                            debugInfo = debugInfo .. "  - Has Screen.Screen (Nested)\n"
                            if innerTycoon.ForcefieldFolder.Screen.Screen:FindFirstChild("SurfaceGui") then
                                debugInfo = debugInfo .. "  - Has SurfaceGui (Nested)\n"
                                if innerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui:FindFirstChild("Time") then
                                    if not timeLabel then -- Only use if we didn't find it in direct method
                                        timeLabel = innerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui.Time
                                    end
                                    debugInfo = debugInfo .. "  - Found Time label (Nested): " .. innerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui.Time.Text .. "\n"
                                else
                                    debugInfo = debugInfo .. "  - No Time label (Nested)\n"
                                end
                            else
                                debugInfo = debugInfo .. "  - No SurfaceGui in Screen (Nested)\n"
                            end
                        else
                            debugInfo = debugInfo .. "  - No Screen.Screen (Nested)\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No Screen (Nested)\n"
                    end

                    -- Check for ForceFieldBuy button (Nested)
                    if innerTycoon.ForcefieldFolder:FindFirstChild("Buttons") then
                        debugInfo = debugInfo .. "  - Has Buttons folder (Nested)\n"
                        if innerTycoon.ForcefieldFolder.Buttons:FindFirstChild("ForceFieldBuy") then
                            debugInfo = debugInfo .. "  - Found ForceFieldBuy button (Nested)\n"
                            if innerTycoon.ForcefieldFolder.Buttons.ForceFieldBuy:FindFirstChild("Forcefield") then
                                if not forceFieldBuy then -- Only use if we didn't find it in direct method
                                    forceFieldBuy = innerTycoon.ForcefieldFolder.Buttons.ForceFieldBuy.Forcefield
                                end
                                debugInfo = debugInfo .. "  - Found Forcefield object (Nested)\n"
                            else
                                debugInfo = debugInfo .. "  - No Forcefield object in ForceFieldBuy (Nested)\n"
                            end
                        else
                            debugInfo = debugInfo .. "  - No ForceFieldBuy button in Buttons (Nested)\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No Buttons folder (Nested)\n"
                    end
                else
                    debugInfo = debugInfo .. "  - No ForcefieldFolder (Nested)\n"
                end
            else
                debugInfo = debugInfo .. "  - No nested Tycoon folder\n"
            end

            -- Final status
            if timeLabel then
                debugInfo = debugInfo .. "Final Countdown: " .. timeLabel.Text .. "\n"
            else
                debugInfo = debugInfo .. "Final Countdown: NOT FOUND\n"
            end

            if forceFieldBuy then
                debugInfo = debugInfo .. "Forcefield Object: FOUND at " .. forceFieldBuy:GetFullName() .. "\n"
            else
                debugInfo = debugInfo .. "Forcefield Object: NOT FOUND\n"
            end

        else
            debugInfo = debugInfo .. "Player Tycoon: Not found\n"
            debugInfo = debugInfo .. "Countdown: N/A\n"
        end
        
        debugInfo = debugInfo .. "Auto Lock Status: " .. (autoLockEnabled and "Enabled" or "Disabled") .. "\n"
        debugInfo = debugInfo .. "=========================="
        
        print(debugInfo)
    end
})

-- PLAYER TAB (Fully Implemented)
-- Speed Modifier
PlayerTab:Slider({
    Title = "Speed Modifier",
    Desc = "Adjust your walking speed",
    Step = 1,
    Value = {
        Min = 1,
        Max = 100,
        Default = 16,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.WalkSpeed = value
        end
    end
})

-- Jump Modifier
PlayerTab:Slider({
    Title = "Jump Modifier",
    Desc = "Adjust your jump power",
    Step = 1,
    Value = {
        Min = 1,
        Max = 200,
        Default = 50,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.JumpPower = value
        end
    end
})

-- Noclip Toggle
PlayerTab:Toggle({
    Title = "Noclip",
    Desc = "Walk through walls and objects",
    Icon = "ghost",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        noclipEnabled = state

        if noclipEnabled then
            noclipConnection = RunService.Stepped:Connect(function()
                if Character then
                    for _, part in pairs(Character:GetChildren()) do
                        if part:IsA("BasePart") and part.CanCollide then
                            part.CanCollide = false
                        end
                    end
                end
            end)
        else
            if noclipConnection then
                noclipConnection:Disconnect()
                noclipConnection = nil
            end

            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
                        part.CanCollide = true
                    end
                end
            end
        end
    end
})

-- Infinite Jump Toggle
PlayerTab:Toggle({
    Title = "Infinite Jump",
    Desc = "Jump infinitely in the air",
    Icon = "arrow-up",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        infJumpEnabled = state
    end
})

-- Infinite Jump Implementation
UserInputService.JumpRequest:Connect(function()
    if infJumpEnabled and Character and Character:FindFirstChild("Humanoid") then
        Character.Humanoid:ChangeState(Enum.HumanoidStateType.Jumping)
    end
end)

-- Character Respawn Handler
LocalPlayer.CharacterAdded:Connect(function(newCharacter)
    Character = newCharacter
    Humanoid = Character:WaitForChild("Humanoid")
    RootPart = Character:WaitForChild("HumanoidRootPart")

    -- Reset values
    originalWalkSpeed = Humanoid.WalkSpeed
    originalJumpPower = Humanoid.JumpPower

    -- Reset auto lock variables
    playerTycoon = nil
    
    -- Reset auto collect variables
    isCollecting = false

    -- Reapply noclip if enabled
    if noclipEnabled and noclipConnection then
        noclipConnection:Disconnect()
        noclipConnection = RunService.Stepped:Connect(function()
            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.CanCollide then
                        part.CanCollide = false
                    end
                end
            end
        end)
    end
end)

-- TELEPORT TAB (Placeholder)
TeleportTab:Toggle({
    Title = "Teleport System",
    Desc = "Teleportation features coming soon",
    Icon = "map-pin",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Teleport System:", state)
        -- TODO: Implement Teleport functionality
    end
})

-- ESP TAB
ESPTab:Toggle({
    Title = "Player ESP",
    Desc = "Show player names and information",
    Icon = "eye",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        playerESPEnabled = state
        
        if playerESPEnabled then
            local function createPlayerESP(player)
                pcall(function()
                    if player == LocalPlayer then return end
                    
                    if playerESPGuis[player] then
                        if playerESPGuis[player].gui and playerESPGuis[player].gui.Parent then
                            playerESPGuis[player].gui:Destroy()
                        end
                        playerESPGuis[player] = nil
                    end
                    
                    local character = player.Character
                    if not character then return end
                    
                    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
                    local humanoid = character:FindFirstChild("Humanoid")
                    if not humanoidRootPart or not humanoid then return end
                    
                    local existingESP = humanoidRootPart:FindFirstChild("PlayerESP")
                    if existingESP then
                        existingESP:Destroy()
                    end
                    
                    local billboardGui = Instance.new("BillboardGui")
                    billboardGui.Name = "PlayerESP"
                    billboardGui.Size = UDim2.new(0, 200, 0, 100)
                    billboardGui.StudsOffset = Vector3.new(0, 2, 0)
                    billboardGui.AlwaysOnTop = true
                    billboardGui.Parent = humanoidRootPart
                    
                    local frame = Instance.new("Frame")
                    frame.Name = "MainFrame"
                    frame.Size = UDim2.new(1, 0, 1, 0)
                    frame.BackgroundTransparency = 1
                    frame.Parent = billboardGui
                    
                    local nameLabel = Instance.new("TextLabel")
                    nameLabel.Name = "NameLabel"
                    nameLabel.Size = UDim2.new(1, 0, 0.4, 0)
                    nameLabel.Position = UDim2.new(0, 0, 0, 0)
                    nameLabel.BackgroundTransparency = 1
                    nameLabel.Text = player.Name
                    nameLabel.TextColor3 = Color3.new(1, 1, 1)
                    nameLabel.TextScaled = true
                    nameLabel.Font = Enum.Font.SourceSansBold
                    nameLabel.TextStrokeTransparency = 0
                    nameLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
                    nameLabel.Parent = frame
                    
                    local healthLabel = Instance.new("TextLabel")
                    healthLabel.Name = "HealthLabel"
                    healthLabel.Size = UDim2.new(1, 0, 0.3, 0)
                    healthLabel.Position = UDim2.new(0, 0, 0.4, 0)
                    healthLabel.BackgroundTransparency = 1
                    healthLabel.Text = "Health: " .. math.floor(humanoid.Health) .. "/" .. math.floor(humanoid.MaxHealth)
                    healthLabel.TextColor3 = Color3.new(0, 1, 0)
                    healthLabel.TextScaled = true
                    healthLabel.Font = Enum.Font.SourceSans
                    healthLabel.TextStrokeTransparency = 0
                    healthLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
                    healthLabel.Parent = frame
                    
                    local distanceLabel = Instance.new("TextLabel")
                    distanceLabel.Name = "DistanceLabel"
                    distanceLabel.Size = UDim2.new(1, 0, 0.3, 0)
                    distanceLabel.Position = UDim2.new(0, 0, 0.7, 0)
                    distanceLabel.BackgroundTransparency = 1
                    distanceLabel.Text = "Distance: 0"
                    distanceLabel.TextColor3 = Color3.new(0.7, 0.7, 0.7)
                    distanceLabel.TextScaled = true
                    distanceLabel.Font = Enum.Font.SourceSans
                    distanceLabel.TextStrokeTransparency = 0
                    distanceLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
                    distanceLabel.Parent = frame
                    
                    playerESPGuis[player] = {
                        gui = billboardGui,
                        nameLabel = nameLabel,
                        healthLabel = healthLabel,
                        distanceLabel = distanceLabel,
                        character = character,
                        humanoid = humanoid,
                        humanoidRootPart = humanoidRootPart
                    }
                end)
            end
            
            local function updatePlayerESP()
                pcall(function()
                    if not playerESPEnabled then return end
                    
                    for player, espData in pairs(playerESPGuis) do
                        if player and player.Parent and espData then
                            local character = player.Character
                            if character and character.Parent then
                                local humanoid = character:FindFirstChild("Humanoid")
                                local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
                                
                                if humanoid and humanoidRootPart and espData.gui and espData.gui.Parent then
                                    if espData.healthLabel and espData.healthLabel.Parent then
                                        local health = math.floor(humanoid.Health)
                                        local maxHealth = math.floor(humanoid.MaxHealth)
                                        espData.healthLabel.Text = "Health: " .. health .. "/" .. maxHealth
                                        
                                        local healthPercent = health / maxHealth
                                        if healthPercent > 0.6 then
                                            espData.healthLabel.TextColor3 = Color3.new(0, 1, 0)
                                        elseif healthPercent > 0.3 then
                                            espData.healthLabel.TextColor3 = Color3.new(1, 1, 0)
                                        else
                                            espData.healthLabel.TextColor3 = Color3.new(1, 0, 0)
                                        end
                                    end
                                    
                                    if espData.distanceLabel and espData.distanceLabel.Parent and RootPart then
                                        local distance = math.floor((RootPart.Position - humanoidRootPart.Position).Magnitude)
                                        espData.distanceLabel.Text = "Distance: " .. distance
                                    end
                                else
                                    playerESPGuis[player] = nil
                                end
                            else
                                playerESPGuis[player] = nil
                            end
                        else
                            playerESPGuis[player] = nil
                        end
                    end
                    
                    for _, player in pairs(Players:GetPlayers()) do
                        if player ~= LocalPlayer and player.Character and not playerESPGuis[player] then
                            createPlayerESP(player)
                        end
                    end
                end)
            end
            
            for _, player in pairs(Players:GetPlayers()) do
                if player ~= LocalPlayer then
                    createPlayerESP(player)
                end
            end
            
            Players.PlayerAdded:Connect(function(player)
                if playerESPEnabled and player ~= LocalPlayer then
                    player.CharacterAdded:Connect(function()
                        wait(1)
                        createPlayerESP(player)
                    end)
                end
            end)
            
            Players.PlayerRemoving:Connect(function(player)
                if playerESPGuis[player] then
                    pcall(function()
                        if playerESPGuis[player].gui then
                            playerESPGuis[player].gui:Destroy()
                        end
                    end)
                    playerESPGuis[player] = nil
                end
            end)
            
            playerESPConnection = RunService.Heartbeat:Connect(updatePlayerESP)
            
        else
            if playerESPConnection then
                playerESPConnection:Disconnect()
                playerESPConnection = nil
            end
            
            for player, espData in pairs(playerESPGuis) do
                pcall(function()
                    if espData and espData.gui and espData.gui.Parent then
                        espData.gui:Destroy()
                    end
                end)
            end
            playerESPGuis = {}
        end
    end
})

-- SETTINGS TAB (Placeholder)
SettingsTab:Toggle({
    Title = "Save Configuration",
    Desc = "Settings features coming soon",
    Icon = "save",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Save Configuration:", state)
        -- TODO: Implement Settings functionality
    end
})

print("Steal A Fish WindUI Script Loaded Successfully!")